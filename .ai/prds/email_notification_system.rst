Transactional Email Notification System with Noticed Gem
========================================================

**Open Questions -Answered **
------------------

1. Job alerts default daily digest for new users.
2. Should we implement in-app notifications alongside emails using Noticed's database delivery? Yes
3. How should we handle email delivery failures and retry logic for critical notifications? No

**Task Checklist**
------------------

**Phase 1: Foundation & EmailPreference Architecture**

☐ Add Noticed gem v2.0 to Gemfile and run installation
☐ Create EmailPreference model with categories and frequency settings
☐ Add Notifiable concern to User model with EmailPreference association
☐ Create base ApplicationNotification class with conditional delivery
☐ Create job lifecycle notification classes
☐ Create message notification classes
☐ Add notification triggers to Job, JobApplication, and Message models
☐ Write unit tests for EmailPreference model and notification classes

**Phase 2: Job Alerts & Onboarding Notifications**

☐ Create JobMatcherService for personalized job matching
☐ Create job alert notification system with frequency controls
☐ Create welcome notification series for new user onboarding
☐ Create profile completion reminder notifications
☐ Add background jobs for job alert processing and scheduling
☐ Add background jobs for welcome series and profile reminders
☐ Create email templates with proper styling and CTAs
☐ Write unit tests for services and background jobs

**Phase 3: Settings Integration & Advanced Features**

☐ Integrate EmailPreference management into Scout/Talent settings
☐ Apply stone color palette to email preference forms
☐ Create notification cleanup job for old notifications
☐ Add comprehensive error handling and delivery monitoring
☐ Enhance email templates with responsive design
☐ Add email delivery status tracking and retry logic
☐ Write integration tests for complete notification workflows

**Phase 1: Foundation & EmailPreference Architecture**
------------------------------------------------------

**Affected Files:**
- ``Gemfile`` - Add noticed gem v2.0 dependency
- ``config/initializers/noticed.rb`` - Noticed configuration
- ``db/migrate/xxx_create_email_preferences.rb`` - EmailPreference model
- ``app/models/email_preference.rb`` - Email preference model with categories
- ``app/models/concerns/notifiable.rb`` - User notification concern
- ``app/models/user.rb`` - Include Notifiable concern
- ``app/notifications/application_notification.rb`` - Base notification class
- ``app/notifications/job_posted_notification.rb`` - Job posted notifications
- ``app/notifications/application_received_notification.rb`` - Application notifications
- ``app/notifications/message_received_notification.rb`` - Message notifications
- ``app/mailers/user_mailer.rb`` - Update for Noticed integration

**Noticed Gem Installation:**

Add to ``Gemfile``::

    gem 'noticed', '~> 2.0'

Run installation::

    bundle install
    rails generate noticed:install
    rails db:migrate

**EmailPreference Model:**

Create ``db/migrate/xxx_create_email_preferences.rb``::

    class CreateEmailPreferences < ActiveRecord::Migration[7.0]
      def change
        create_table :email_preferences do |t|
          t.references :user, null: false, foreign_key: true

          # Notification categories
          t.boolean :job_notifications, default: true
          t.boolean :message_notifications, default: true
          t.boolean :account_notifications, default: true

          # Job alert frequency
          t.integer :job_alert_frequency, default: 1 # 0: real_time, 1: daily, 2: weekly

          t.timestamps
        end

        add_index :email_preferences, :user_id, unique: true
      end
    end

Create ``app/models/email_preference.rb``::

    class EmailPreference < ApplicationRecord
      belongs_to :user

      enum job_alert_frequency: { real_time: 0, daily: 1, weekly: 2 }

      def can_receive?(category)
        case category
        when :job_notifications then job_notifications?
        when :message_notifications then message_notifications?
        when :account_notifications then account_notifications?
        else true # Allow transactional emails by default
        end
      end

      def self.categories
        {
          job_notifications: {
            label: 'Job updates',
            description: 'New jobs, applications, and status changes'
          },
          message_notifications: {
            label: 'Messages',
            description: 'New messages from scouts or talent'
          },
          account_notifications: {
            label: 'Account updates',
            description: 'Welcome emails and profile reminders'
          }
        }
      end
    end

**Notifiable Concern:**

Create ``app/models/concerns/notifiable.rb``::

    module Notifiable
      extend ActiveSupport::Concern

      included do
        has_many :notifications, as: :recipient, dependent: :destroy, class_name: "Noticed::Notification"
        has_one :email_preference, dependent: :destroy

        after_create :create_default_email_preference
      end

      def can_receive_email?(category)
        return false unless verified?
        email_preference&.can_receive?(category) != false
      end

      private

      def create_default_email_preference
        EmailPreference.create!(user: self)
      end
    end

Update ``app/models/user.rb``::

    class User < ApplicationRecord
      include Notifiable
      # ... existing code ...
    end

**Base Notification Classes:**

Create ``app/notifications/application_notification.rb``::

    class ApplicationNotification < Noticed::Event
      deliver_by :database

      # Email delivery with preference check
      deliver_by :email do |config|
        config.mailer = "UserMailer"
        config.if = ->(notification) {
          recipient = notification.recipient
          recipient.can_receive_email?(notification.email_category)
        }
      end

      # Override in subclasses
      def email_category
        :account_notifications
      end

      # Helper to get the recipient user
      def recipient_user
        recipient.is_a?(User) ? recipient : recipient.user
      end
    end

**Job Notification Classes:**

Create ``app/notifications/job_posted_notification.rb``::

    class JobPostedNotification < ApplicationNotification
      deliver_by :email do |config|
        config.mailer = "UserMailer"
        config.method = :job_posted
        config.if = ->(notification) {
          notification.recipient_user.can_receive_email?(:job_notifications)
        }
      end

      param :job

      def email_category
        :job_notifications
      end

      def message
        "Your job '#{params[:job].title}' has been posted successfully"
      end

      def url
        job_path(params[:job])
      end
    end

Create ``app/notifications/application_received_notification.rb``::

    class ApplicationReceivedNotification < ApplicationNotification
      deliver_by :email do |config|
        config.mailer = "UserMailer"
        config.method = :application_received
        config.if = ->(notification) {
          notification.recipient_user.can_receive_email?(:job_notifications)
        }
      end

      param :application
      param :job
      param :milestone # nil, :first, :five, :ten, :twenty

      def email_category
        :job_notifications
      end

      def message
        case params[:milestone]
        when :first
          "You received your first application for '#{params[:job].title}'"
        when :five, :ten, :twenty
          "#{params[:milestone] == :twenty ? '20+' : params[:milestone].to_s.capitalize} applications received for '#{params[:job].title}'"
        else
          "New application received for '#{params[:job].title}'"
        end
      end
    end

Create ``app/notifications/message_received_notification.rb``::

    class MessageReceivedNotification < ApplicationNotification
      deliver_by :email do |config|
        config.mailer = "UserMailer"
        config.method = :message_received
        config.if = ->(notification) {
          notification.recipient_user.can_receive_email?(:message_notifications)
        }
      end

      param :message
      param :conversation

      def email_category
        :message_notifications
      end

      def message
        "New message from #{params[:message].sender.full_name}"
      end
    end

**Model Integration:**

Add notification triggers to ``app/models/job.rb``::

    after_create :send_job_posted_notification

    private

    def send_job_posted_notification
      JobPostedNotification.with(job: self).deliver(organization.primary_user)
    end

Add notification triggers to ``app/models/job_application.rb``::

    after_create :notify_scout_of_application

    private

    def notify_scout_of_application
      milestone = determine_application_milestone
      ApplicationReceivedNotification.with(
        application: self,
        job: job,
        milestone: milestone
      ).deliver(job.organization.primary_user)
    end

    def determine_application_milestone
      count = job.job_applications.count
      case count
      when 1 then :first
      when 5 then :five
      when 10 then :ten
      when 20 then :twenty
      else nil
      end
    end

**Unit Tests:**

- ``test/models/email_preference_test.rb`` - Test EmailPreference model methods
- ``test/notifications/job_posted_notification_test.rb`` - Test job notification delivery
- ``test/notifications/application_received_notification_test.rb`` - Test application notifications
- ``test/notifications/message_received_notification_test.rb`` - Test message notifications
- ``test/models/user_test.rb`` - Test Notifiable concern integration

**Phase 2: Job Alerts & Onboarding Notifications**
---------------------------------------------------

**Affected Files:**
- ``app/services/job_matcher_service.rb`` - Job matching logic for alerts
- ``app/notifications/job_alert_notification.rb`` - Job alert notifications
- ``app/notifications/welcome_notification.rb`` - Welcome email series
- ``app/notifications/profile_completion_notification.rb`` - Profile reminders
- ``app/jobs/send_job_alerts_job.rb`` - Job alert processing
- ``app/jobs/send_welcome_series_job.rb`` - Welcome email scheduling
- ``app/jobs/check_profile_completion_job.rb`` - Profile completion checks
- ``app/mailers/user_mailer.rb`` - Add new email methods
- ``app/views/user_mailer/`` - Email templates

**Job Matching Service:**

Create ``app/services/job_matcher_service.rb``::

    class JobMatcherService
      def initialize(talent)
        @talent = talent
      end

      def find_matching_jobs(since: 1.day.ago)
        return Job.none unless @talent.skills.any?

        Job.published
           .where(created_at: since..)
           .joins(:skills)
           .where(skills: { id: @talent.skill_ids })
           .where.not(id: @talent.job_applications.pluck(:job_id))
           .distinct
           .limit(10)
      end
    end

**Job Alert Notifications:**

Create ``app/notifications/job_alert_notification.rb``::

    class JobAlertNotification < ApplicationNotification
      deliver_by :email do |config|
        config.mailer = "UserMailer"
        config.method = :job_alert
        config.if = ->(notification) {
          notification.recipient_user.can_receive_email?(:job_notifications)
        }
      end

      param :jobs
      param :frequency # :real_time, :daily, :weekly

      def email_category
        :job_notifications
      end

      def message
        if params[:frequency] == :real_time && params[:jobs].count == 1
          "New job: #{params[:jobs].first.title}"
        else
          "#{params[:jobs].count} new jobs match your skills"
        end
      end
    end

**Welcome Notifications:**

Create ``app/notifications/welcome_notification.rb``::

    class WelcomeNotification < ApplicationNotification
      deliver_by :email do |config|
        config.mailer = "UserMailer"
        config.method = ->(notification) {
          "welcome_#{notification.params[:step]}_#{notification.params[:user_type]}"
        }
        config.if = ->(notification) {
          notification.recipient.can_receive_email?(:account_notifications)
        }
      end

      param :step # :day1, :day3, :day7
      param :user_type # :scout, :talent

      def email_category
        :account_notifications
      end

      def message
        case params[:step]
        when :day1 then "Welcome to Ghostwrote!"
        when :day3 then "Tips to get started on Ghostwrote"
        when :day7 then "Success stories from our community"
        end
      end
    end

**Profile Completion Notifications:**

Create ``app/notifications/profile_completion_notification.rb``::

    class ProfileCompletionNotification < ApplicationNotification
      deliver_by :email do |config|
        config.mailer = "UserMailer"
        config.method = :profile_completion_reminder
        config.if = ->(notification) {
          notification.recipient.can_receive_email?(:account_notifications)
        }
      end

      param :missing_fields
      param :user_type # :scout, :talent

      def email_category
        :account_notifications
      end

      def message
        "Complete your profile to #{params[:user_type] == :scout ? 'attract top talent' : 'get more opportunities'}"
      end
    end

**Background Jobs:**

Create ``app/jobs/send_job_alerts_job.rb``::

    class SendJobAlertsJob < ApplicationJob
      queue_as :default

      def perform(frequency = :daily)
        talents = User.joins(:talent_profile, :email_preference)
                     .where(email_preferences: {
                       job_notifications: true,
                       job_alert_frequency: frequency
                     })

        talents.find_each do |talent|
          matcher = JobMatcherService.new(talent.talent_profile)

          since = case frequency
                  when :real_time then 1.hour.ago
                  when :daily then 1.day.ago
                  when :weekly then 1.week.ago
                  end

          matching_jobs = matcher.find_matching_jobs(since: since)

          next unless matching_jobs.any?

          JobAlertNotification.with(
            jobs: matching_jobs,
            frequency: frequency
          ).deliver(talent)
        end
      end
    end

Create ``app/jobs/send_welcome_series_job.rb``::

    class SendWelcomeSeriesJob < ApplicationJob
      queue_as :default

      def perform(user_id, step)
        user = User.find(user_id)
        return unless user.can_receive_email?(:account_notifications)

        user_type = user.organizations.any? ? :scout : :talent

        WelcomeNotification.with(
          step: step,
          user_type: user_type
        ).deliver(user)

        # Schedule next email in series
        case step
        when :day1
          self.class.set(wait: 2.days).perform_later(user_id, :day3)
        when :day3
          self.class.set(wait: 4.days).perform_later(user_id, :day7)
        end
      end
    end

Add welcome series trigger to ``app/models/user.rb``::

    after_create :schedule_welcome_series

    private

    def schedule_welcome_series
      SendWelcomeSeriesJob.perform_later(id, :day1)
    end

**Unit Tests:**

- ``test/services/job_matcher_service_test.rb`` - Test job matching logic
- ``test/notifications/job_alert_notification_test.rb`` - Test job alert delivery
- ``test/notifications/welcome_notification_test.rb`` - Test welcome notifications
- ``test/jobs/send_job_alerts_job_test.rb`` - Test job alert processing
- ``test/jobs/send_welcome_series_job_test.rb`` - Test welcome series scheduling

**Phase 3: Settings Integration & Advanced Features**
-----------------------------------------------------

**Affected Files:**
- ``app/views/scout/settings/show.html.erb`` - Add email preference section
- ``app/views/talent/settings/show.html.erb`` - Add email preference section
- ``app/views/shared/_email_preferences.html.erb`` - Shared preference form partial
- ``app/controllers/scout/settings_controller.rb`` - Handle EmailPreference updates
- ``app/controllers/talent/settings_controller.rb`` - Handle EmailPreference updates
- ``app/jobs/cleanup_notifications_job.rb`` - Notification cleanup job
- ``app/views/user_mailer/`` - Enhanced email templates with styling

**Settings UI Integration:**

Add email preferences section to Scout settings using stone color palette::

    <!-- In app/views/scout/settings/show.html.erb -->
    <div class="p-6 mb-6 bg-white border rounded-lg border-stone-200">
      <h3 class="mb-4 text-lg font-medium text-stone-900">Email Notifications</h3>
      <%= render 'shared/email_preferences', user: @user %>
    </div>

Create shared email preferences partial::

    <!-- In app/views/shared/_email_preferences.html.erb -->
    <%= form_with model: user.email_preference, url: update_email_preferences_path, local: true, class: "space-y-4" do |form| %>
      <div class="space-y-4">
        <% EmailPreference.categories.each do |key, config| %>
          <label class="flex items-start">
            <%= form.check_box key, class: "mt-1 rounded border-stone-300 text-stone-600 focus:ring-stone-500" %>
            <div class="ml-3">
              <span class="text-sm font-medium text-stone-700"><%= config[:label] %></span>
              <p class="text-xs text-stone-500"><%= config[:description] %></p>
            </div>
          </label>
        <% end %>

        <% if user.talent_profile.present? %>
          <div class="pt-4 border-t border-stone-200">
            <label class="block mb-2 text-sm font-medium text-stone-700">Job Alert Frequency</label>
            <%= form.select :job_alert_frequency,
                           options_for_select([
                             ['Real-time', 'real_time'],
                             ['Daily digest', 'daily'],
                             ['Weekly digest', 'weekly']
                           ], form.object.job_alert_frequency),
                           {},
                           { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500" } %>
          </div>
        <% end %>
      </div>

      <div class="pt-4">
        <%= form.submit 'Save Preferences', class: 'bg-stone-600 text-white px-4 py-2 rounded-md hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500' %>
      </div>
    <% end %>

**Enhanced Email Templates:**

Create pla styling and CTAs in ``app/views/user_mailer/`` directory with improved UX copy and visual design.

**Unit Tests:**

- ``test/controllers/scout/settings_controller_test.rb`` - Test email preference updates
- ``test/controllers/talent/settings_controller_test.rb`` - Test email preference updates
- ``test/jobs/cleanup_notifications_job_test.rb`` - Test notification cleanup
- ``test/integration/email_notification_flow_test.rb`` - Test complete notification workflows
